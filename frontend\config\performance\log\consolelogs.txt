Test 17997 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17997 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17997 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17997 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:1598 Grace period (25s) expired for test 17997, removing from active tests, status: passed
api-integration.js:2888 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1570 Applied 'undefined' filter: 0/0 tests included
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 7 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=7, Passed=5, Failed=2, Running=0
api-integration.js:1598 Grace period (25s) expired for test 17997, removing from active tests, status: passed
api-integration.js:2888 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1570 Applied 'undefined' filter: 0/0 tests included
api-integration.js:555 Completed poll request #1, releasing lock
api-integration.js:2439 Run button clicked for suite: DEMO PE2.1 Sanity Test (312)
api-integration.js:2182 Loading: Running test suite 312...
api-service.js:109 Running test suite with ID: 312
unified-api-service.js:364 Making POST request to: http://localhost:3000/api/suite-runner
unified-api-service.js:403 Request parameters: {ts_id: '312'}
api-integration.js:101 TSN ID is an object, attempting to extract ID: {success: true, testId: undefined, message: 'Test suite 312 started successfully with session ID 17998'}
api-integration.js:109 Extracted session ID from message: 17998
api-integration.js:2215 Success: Test suite 312 started successfully. Run ID: 17998
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17998 (original map key: 17998): {status: 'running', type: 'Test Suite', id: '312', ts_id: '312', startTime: Mon Jul 28 2025 14:02:58 GMT+0300 (Eastern European Summer Time), …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17998: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17998 - Using test data from: activeTests map, status: running
api-integration.js:1841 Test status for 17998: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', tsn_id: '17998', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17998: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', sessionId: '17998', status: 'running', statusText: 'Running', …}
api-integration.js:2200 Loading complete
api-integration.js:451 Starting poll request #2
api-integration.js:461 Using since_id=17996 for incremental polling (highestRecentTsnId=17997)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 17998 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 17998 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 17998 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 17998 - Raw end time value: "2025-07-28 11:02:58" (type: string)
api-integration.js:772 Test 17998 - Timezone fix applied: "2025-07-28 11:02:58" -> corrected for 3h offset -> 2025-07-28T11:02:58.000Z
api-integration.js:779 Test 17998 - Parsed end time: 2025-07-28T11:02:58.000Z, Current time: 2025-07-28T11:03:05.234Z
api-integration.js:780 Test 17998 - Parsed end time (local): Mon Jul 28 2025 14:02:58 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:03:05 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 17998 - End time timestamp: 1753700578000, Current time timestamp: 1753700585234
api-integration.js:787 Test 17998 - Recent completion (7.2s ago), keeping as running to allow test results to be written
api-integration.js:677 Skipping test 17997 as it was previously marked as completed
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 8 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=8, Passed=5, Failed=2, Running=1
api-integration.js:2879 Smart merge for test 17998: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17998 (original map key: 17998): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:02:58', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17998: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17998 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17998 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17998 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17998: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '17998', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17998: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '17998', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #2, releasing lock
api-integration.js:451 Starting poll request #3
api-integration.js:461 Using since_id=17997 for incremental polling (highestRecentTsnId=17998)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 17998 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 17998 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 17998 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 17998 - Raw end time value: "2025-07-28 11:02:58" (type: string)
api-integration.js:772 Test 17998 - Timezone fix applied: "2025-07-28 11:02:58" -> corrected for 3h offset -> 2025-07-28T11:02:58.000Z
api-integration.js:779 Test 17998 - Parsed end time: 2025-07-28T11:02:58.000Z, Current time: 2025-07-28T11:03:15.236Z
api-integration.js:780 Test 17998 - Parsed end time (local): Mon Jul 28 2025 14:02:58 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:03:15 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 17998 - End time timestamp: 1753700578000, Current time timestamp: 1753700595236
api-integration.js:787 Test 17998 - Recent completion (17.2s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 8 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=8, Passed=5, Failed=2, Running=1
api-integration.js:2879 Smart merge for test 17998: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17998 (original map key: 17998): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:02:58', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17998: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17998 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17998 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17998 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17998: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '17998', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17998: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '17998', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #3, releasing lock
api-integration.js:451 Starting poll request #4
api-integration.js:461 Using since_id=17997 for incremental polling (highestRecentTsnId=17998)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 17998 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 17998 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 17998 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 17998 - Raw end time value: "2025-07-28 11:03:15" (type: string)
api-integration.js:772 Test 17998 - Timezone fix applied: "2025-07-28 11:03:15" -> corrected for 3h offset -> 2025-07-28T11:03:15.000Z
api-integration.js:779 Test 17998 - Parsed end time: 2025-07-28T11:03:15.000Z, Current time: 2025-07-28T11:03:25.264Z
api-integration.js:780 Test 17998 - Parsed end time (local): Mon Jul 28 2025 14:03:15 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:03:25 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 17998 - End time timestamp: 1753700595000, Current time timestamp: 1753700605264
api-integration.js:787 Test 17998 - Recent completion (10.3s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 8 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=8, Passed=5, Failed=2, Running=1
api-integration.js:2879 Smart merge for test 17998: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17998 (original map key: 17998): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:02:58', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17998: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17998 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17998 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17998 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17998: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '17998', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17998: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '17998', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #4, releasing lock
api-integration.js:451 Starting poll request #5
api-integration.js:461 Using since_id=17997 for incremental polling (highestRecentTsnId=17998)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 17998 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 17998 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 17998 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 17998 - Raw end time value: "2025-07-28 11:03:31" (type: string)
api-integration.js:772 Test 17998 - Timezone fix applied: "2025-07-28 11:03:31" -> corrected for 3h offset -> 2025-07-28T11:03:31.000Z
api-integration.js:779 Test 17998 - Parsed end time: 2025-07-28T11:03:31.000Z, Current time: 2025-07-28T11:03:35.251Z
api-integration.js:780 Test 17998 - Parsed end time (local): Mon Jul 28 2025 14:03:31 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:03:35 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 17998 - End time timestamp: 1753700611000, Current time timestamp: 1753700615251
api-integration.js:787 Test 17998 - Recent completion (4.3s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 8 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=8, Passed=5, Failed=2, Running=1
api-integration.js:2879 Smart merge for test 17998: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17998 (original map key: 17998): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:02:58', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17998: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17998 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17998 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17998 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17998: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '17998', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17998: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '17998', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #5, releasing lock
api-integration.js:451 Starting poll request #6
api-integration.js:461 Using since_id=17997 for incremental polling (highestRecentTsnId=17998)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 17998 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17998 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17998 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17998 has completed with status: failed (will be visible for 15-second grace period)
dashboard.js:5 UI NOTIFICATION to be displayed [error]: Suite Finished: PE2.1 Sanity Test - Status: Failed. Cases Passed: 3, Cases Failed: 2.
api-integration.js:1598 Grace period (25s) expired for test 17998, removing from active tests, status: failed
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17998 (original map key: N/A): {tsn_id: '17998', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:02:58', endTime: '2025-07-28 11:02:58', …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17998: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17998 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17998 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17998 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17998: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '17998', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17998: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '17998', status: 'running', statusText: 'Running', …}
api-integration.js:1458 Found 1 new completed tests to count in dashboard
api-integration.js:1472 Passed tests: 0, Failed tests: 1
api-integration.js:1483 Updated dashboard counters: {total: 9, successful: 5, failed: 3}
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 8 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=8, Passed=5, Failed=2, Running=1
api-integration.js:1598 Grace period (25s) expired for test 17998, removing from active tests, status: failed
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17998 (original map key: N/A): {tsn_id: '17998', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:02:58', endTime: '2025-07-28 11:02:58', …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17998: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17998 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17998 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17998 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17998: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '17998', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17998: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '17998', status: 'running', statusText: 'Running', …}
api-integration.js:510 Performing full recent runs refresh
api-integration.js:514 Starting full refresh as part of request #6
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:580 Skipping poll because previous request is still in progress
api-integration.js:533 Full refresh received 100 runs, updating recentRunsCache
api-integration.js:677 Skipping test 17998 as it was previously marked as completed
api-integration.js:677 Skipping test 17997 as it was previously marked as completed
api-integration.js:628 Test 17996 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17996 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17996 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17996 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17995 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17995 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17995 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17995 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17994 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17994 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17994 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17994 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17993 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17993 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17993 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17993 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17992 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17992 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17992 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17992 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17991 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17991 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17991 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17991 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17990 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17990 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17990 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17990 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17989 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17989 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17989 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17989 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17988 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17988 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17988 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17988 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17987 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17987 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17987 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17987 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17986 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17986 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17986 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17986 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17985 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17985 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17985 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17985 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17984 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17984 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17984 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17984 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17983 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17983 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17983 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17983 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17982 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17982 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17982 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17982 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17981 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17981 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17981 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17981 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17980 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17980 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17980 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17980 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17979 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17979 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17979 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17979 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17978 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17978 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17978 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17978 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17977 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17977 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17977 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17977 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17976 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17976 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17976 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17976 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17975 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17975 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17975 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17975 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17974 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17974 completion data: {passed_cases: 11, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17974 - Detected as PASSED: passed_cases=11, passed=undefined
api-integration.js:802 Test 17974 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17973 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17973 completion data: {passed_cases: 10, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17973 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17973 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17972 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17972 completion data: {passed_cases: 10, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17972 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17972 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17971 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17971 completion data: {passed_cases: 11, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17971 - Detected as PASSED: passed_cases=11, passed=undefined
api-integration.js:802 Test 17971 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17970 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17970 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17970 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17970 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17969 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17969 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17969 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17969 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17968 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17968 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17968 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:802 Test 17968 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17967 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17967 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17967 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17967 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17966 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17966 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17966 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17966 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17965 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17965 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17965 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17965 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17964 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17964 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17964 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:802 Test 17964 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17963 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17963 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17963 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17963 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17962 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17962 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17962 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:802 Test 17962 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17961 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17961 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17961 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17961 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17960 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17960 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17960 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17960 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17959 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17959 completion data: {passed_cases: 5, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17959 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17959 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17958 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17958 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17958 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17958 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17957 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17957 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17957 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17957 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17956 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17956 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17956 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17956 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17955 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17955 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17955 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17955 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17954 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17954 completion data: {passed_cases: 5, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17954 - Detected as PASSED: passed_cases=5, passed=undefined
api-integration.js:802 Test 17954 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17953 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17953 completion data: {passed_cases: 5, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17953 - Detected as PASSED: passed_cases=5, passed=undefined
api-integration.js:802 Test 17953 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17952 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17952 completion data: {passed_cases: 7, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17952 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17952 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17951 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17951 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17951 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17951 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17950 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17950 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17950 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17950 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17949 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17949 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17949 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17949 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17948 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17948 completion data: {passed_cases: 0, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17948 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 17948 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17947 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17947 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17947 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17947 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17946 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17946 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17946 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17946 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17945 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17945 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17945 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17945 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17944 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17944 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17944 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17944 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17943 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17943 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17943 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17943 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17942 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17942 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17942 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17942 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17941 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17941 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17941 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17941 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17940 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17940 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17940 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17940 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17939 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17939 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17939 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17939 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17938 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17938 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17938 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17938 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17937 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17937 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17937 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17937 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17936 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17936 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17936 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17936 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17935 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17935 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17935 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17935 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17934 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17934 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17934 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17934 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17933 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17933 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17933 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17933 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17932 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17932 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17932 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17932 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17931 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17931 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17931 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17931 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17930 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17930 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17930 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17930 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17929 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17929 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17929 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17929 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17928 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17928 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17928 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17928 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17927 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17927 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17927 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17927 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17926 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17926 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17926 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17926 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17925 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17925 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17925 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17925 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17924 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17924 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17924 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17924 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17923 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17923 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17923 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17923 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17922 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17922 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17922 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17922 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17921 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17921 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17921 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17921 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17920 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17920 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17920 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17920 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17919 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17919 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17919 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17919 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17918 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17918 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17918 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17918 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17917 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17917 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17917 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17917 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17916 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17916 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17916 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17916 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17915 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17915 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17915 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17915 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17914 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17914 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17914 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17914 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17913 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17913 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17913 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17913 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17912 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17912 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17912 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17912 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17911 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17911 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17911 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17911 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17910 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17910 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17910 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17910 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17909 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17909 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17909 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17909 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17908 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17908 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17908 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17908 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17907 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17907 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17907 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17907 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17906 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17906 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17906 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17906 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17905 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17905 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17905 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17905 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17904 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17904 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17904 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17904 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17903 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17903 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17903 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17903 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17902 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17902 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17902 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17902 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17901 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 17901 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 17901 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:802 Test 17901 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:628 Test 17900 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17900 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17900 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17900 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:628 Test 17899 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17899 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17899 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:802 Test 17899 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:1598 Grace period (25s) expired for test 17996, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17995, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17994, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17993, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17992, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17991, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17990, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17989, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17988, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17987, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17986, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17985, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17984, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17983, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17982, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17981, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17980, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17979, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17978, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17977, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17976, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17975, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17974, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17973, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17972, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17971, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17970, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17969, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17968, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17967, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17966, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17965, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17964, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17963, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17962, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17961, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17960, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17959, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17958, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17957, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17956, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17955, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17954, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17953, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17952, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17951, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17950, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17949, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17948, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17947, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17946, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17945, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17944, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17943, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17942, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17941, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17940, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17939, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17938, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17937, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17936, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17935, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17934, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17933, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17932, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17931, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17930, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17929, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17928, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17927, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17926, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17925, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17924, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17923, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17922, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17921, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17920, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17919, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17918, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17917, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17916, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17915, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17914, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17913, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17912, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17911, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17910, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17909, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17908, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17907, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17906, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17905, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17904, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17903, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17902, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17901, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17900, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17899, removing from active tests, status: passed
api-integration.js:2888 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1570 Applied 'undefined' filter: 0/0 tests included
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 8 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=8, Passed=5, Failed=3, Running=0
api-integration.js:1598 Grace period (25s) expired for test 17996, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17995, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17994, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17993, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17992, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17991, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17990, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17989, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17988, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17987, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17986, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17985, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17984, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17983, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17982, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17981, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17980, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17979, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17978, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17977, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17976, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17975, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17974, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17973, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17972, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17971, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17970, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17969, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17968, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17967, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17966, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17965, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17964, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17963, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17962, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17961, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17960, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17959, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17958, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17957, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17956, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17955, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17954, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17953, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17952, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17951, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17950, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17949, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17948, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17947, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17946, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17945, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17944, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17943, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17942, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17941, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17940, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17939, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17938, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17937, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17936, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17935, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17934, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17933, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17932, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17931, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17930, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17929, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17928, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17927, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17926, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17925, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17924, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17923, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17922, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17921, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17920, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17919, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17918, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17917, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17916, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17915, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17914, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17913, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17912, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17911, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17910, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17909, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17908, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17907, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17906, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17905, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17904, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17903, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17902, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17901, removing from active tests, status: failed
api-integration.js:1598 Grace period (25s) expired for test 17900, removing from active tests, status: passed
api-integration.js:1598 Grace period (25s) expired for test 17899, removing from active tests, status: passed
api-integration.js:2888 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1643 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1570 Applied 'undefined' filter: 0/0 tests included
api-integration.js:555 Completed poll request #6, releasing lock
api-integration.js:451 Starting poll request #7
api-integration.js:461 Using since_id=17997 for incremental polling (highestRecentTsnId=17998)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:2439 Run button clicked for suite: DEMO PE2.1 Heartbeat Test (323)
api-integration.js:2182 Loading: Running test suite 323...
api-service.js:109 Running test suite with ID: 323
unified-api-service.js:364 Making POST request to: http://localhost:3000/api/suite-runner
unified-api-service.js:403 Request parameters: {ts_id: '323'}
api-integration.js:101 TSN ID is an object, attempting to extract ID: {success: true, testId: undefined, message: 'Test suite 323 started successfully with session ID 17999'}
api-integration.js:109 Extracted session ID from message: 17999
api-integration.js:2215 Success: Test suite 323 started successfully. Run ID: 17999
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: '323', startTime: Mon Jul 28 2025 14:04:04 GMT+0300 (Eastern European Summer Time), …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17999: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'DEMO PE2.1 Heartbeat Test', testId: '323', tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'DEMO PE2.1 Heartbeat Test', testId: '323', sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:2200 Loading complete
api-integration.js:628 Test 17999 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 17999 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 17999 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 17999 - Raw end time value: "2025-07-28 11:04:05" (type: string)
api-integration.js:772 Test 17999 - Timezone fix applied: "2025-07-28 11:04:05" -> corrected for 3h offset -> 2025-07-28T11:04:05.000Z
api-integration.js:779 Test 17999 - Parsed end time: 2025-07-28T11:04:05.000Z, Current time: 2025-07-28T11:04:05.220Z
api-integration.js:780 Test 17999 - Parsed end time (local): Mon Jul 28 2025 14:04:05 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:04:05 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 17999 - End time timestamp: 1753700645000, Current time timestamp: 1753700645220
api-integration.js:787 Test 17999 - Recent completion (0.2s ago), keeping as running to allow test results to be written
api-integration.js:677 Skipping test 17998 as it was previously marked as completed
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 9 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=9, Passed=5, Failed=3, Running=1
api-integration.js:2879 Smart merge for test 17999: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-07-28 11:04:04', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17999: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #7, releasing lock
api-integration.js:451 Starting poll request #8
api-integration.js:461 Using since_id=17998 for incremental polling (highestRecentTsnId=17999)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 17999 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 17999 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 17999 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 17999 - Raw end time value: "2025-07-28 11:04:05" (type: string)
api-integration.js:772 Test 17999 - Timezone fix applied: "2025-07-28 11:04:05" -> corrected for 3h offset -> 2025-07-28T11:04:05.000Z
api-integration.js:779 Test 17999 - Parsed end time: 2025-07-28T11:04:05.000Z, Current time: 2025-07-28T11:04:15.230Z
api-integration.js:780 Test 17999 - Parsed end time (local): Mon Jul 28 2025 14:04:05 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:04:15 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 17999 - End time timestamp: 1753700645000, Current time timestamp: 1753700655230
api-integration.js:787 Test 17999 - Recent completion (10.2s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 9 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=9, Passed=5, Failed=3, Running=1
api-integration.js:2879 Smart merge for test 17999: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-07-28 11:04:04', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: undefined Current user: <EMAIL>
api-integration.js:1566 Test 17999: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1570 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #8, releasing lock
api-integration.js:2460 Active tests filter set to: mine
api-integration.js:2879 Smart merge for test 17999: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-07-28 11:04:04', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: mine Current user: <EMAIL>
api-integration.js:1566 Test 17999: user=<EMAIL>, filter=mine, isMyTest=true, include=true
api-integration.js:1570 Applied 'mine' filter: 1/1 tests included
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:2403 [API Integration] Active tests filter set to: mine
api-integration.js:2410 [API Integration] Re-rendering active tests due to filter change.
api-integration.js:2879 Smart merge for test 17999: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-07-28 11:04:04', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: mine Current user: <EMAIL>
api-integration.js:1566 Test 17999: user=<EMAIL>, filter=mine, isMyTest=true, include=true
api-integration.js:1570 Applied 'mine' filter: 1/1 tests included
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:451 Starting poll request #9
api-integration.js:461 Using since_id=17998 for incremental polling (highestRecentTsnId=17999)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:2460 Active tests filter set to: all
api-integration.js:2879 Smart merge for test 17999: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-07-28 11:04:04', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:2403 [API Integration] Active tests filter set to: all
api-integration.js:2410 [API Integration] Re-rendering active tests due to filter change.
api-integration.js:2879 Smart merge for test 17999: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-07-28 11:04:04', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:2439 Run button clicked for suite: DEMO PE2.1 Sanity Test (312)
api-integration.js:2182 Loading: Running test suite 312...
api-service.js:109 Running test suite with ID: 312
unified-api-service.js:364 Making POST request to: http://localhost:3000/api/suite-runner
unified-api-service.js:403 Request parameters: {ts_id: '312'}
api-integration.js:101 TSN ID is an object, attempting to extract ID: {success: true, testId: undefined, message: 'Test suite 312 started successfully with session ID 18000'}
api-integration.js:109 Extracted session ID from message: 18000
api-integration.js:2215 Success: Test suite 312 started successfully. Run ID: 18000
api-integration.js:2879 Smart merge for test 17999: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 3 raw tests merged to 2 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: 17999): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-07-28 11:04:04', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: 18000): {status: 'running', type: 'Test Suite', id: '312', ts_id: '312', startTime: Mon Jul 28 2025 14:04:21 GMT+0300 (Eastern European Summer Time), …}
api-integration.js:1643 Deduplicated 3 raw tests to 2 processed tests
api-integration.js:1655 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 2 tests
api-integration.js:1724 Test 17999 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: activeTests map, status: running
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:2200 Loading complete
api-integration.js:628 Test 18000 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 18000 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 18000 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 18000 - Raw end time value: "2025-07-28 11:04:22" (type: string)
api-integration.js:772 Test 18000 - Timezone fix applied: "2025-07-28 11:04:22" -> corrected for 3h offset -> 2025-07-28T11:04:22.000Z
api-integration.js:779 Test 18000 - Parsed end time: 2025-07-28T11:04:22.000Z, Current time: 2025-07-28T11:04:25.504Z
api-integration.js:780 Test 18000 - Parsed end time (local): Mon Jul 28 2025 14:04:22 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:04:25 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 18000 - End time timestamp: 1753700662000, Current time timestamp: 1753700665504
api-integration.js:787 Test 18000 - Recent completion (3.5s ago), keeping as running to allow test results to be written
api-integration.js:628 Test 17999 has end_time, keeping active for grace period (status: Passed)
api-integration.js:709 Test 17999 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:727 Test 17999 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:802 Test 17999 has completed with status: passed (will be visible for 15-second grace period)
dashboard.js:5 UI NOTIFICATION to be displayed [success]: Suite Finished: PE2.1 Heartbeat Test - Status: Passed. Cases Passed: 2, Cases Failed: 0.
api-integration.js:1598 Grace period (25s) expired for test 17999, removing from active tests, status: passed
api-integration.js:2879 Smart merge for test 18000: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 3 raw tests merged to 2 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: 18000): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:22', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: N/A): {tsn_id: '17999', tc_id: null, ts_id: 333, startTime: '2025-07-28 11:04:04', endTime: '2025-07-28 11:04:05', …}
api-integration.js:1643 Deduplicated 3 raw tests to 2 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 2 tests
api-integration.js:1724 Test 17999 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18000 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18000 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:1458 Found 1 new completed tests to count in dashboard
api-integration.js:1472 Passed tests: 1, Failed tests: 0
api-integration.js:1483 Updated dashboard counters: {total: 10, successful: 6, failed: 3}
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 10 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=10, Passed=5, Failed=3, Running=2
api-integration.js:1598 Grace period (25s) expired for test 17999, removing from active tests, status: passed
api-integration.js:2879 Smart merge for test 18000: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 3 raw tests merged to 2 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: 18000): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:22', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: N/A): {tsn_id: '17999', tc_id: null, ts_id: 333, startTime: '2025-07-28 11:04:04', endTime: '2025-07-28 11:04:05', …}
api-integration.js:1643 Deduplicated 3 raw tests to 2 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 2 tests
api-integration.js:1724 Test 17999 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18000 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18000 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #9, releasing lock
api-integration.js:451 Starting poll request #10
api-integration.js:461 Using since_id=17999 for incremental polling (highestRecentTsnId=18000)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 18000 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 18000 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 18000 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 18000 - Raw end time value: "2025-07-28 11:04:32" (type: string)
api-integration.js:772 Test 18000 - Timezone fix applied: "2025-07-28 11:04:32" -> corrected for 3h offset -> 2025-07-28T11:04:32.000Z
api-integration.js:779 Test 18000 - Parsed end time: 2025-07-28T11:04:32.000Z, Current time: 2025-07-28T11:04:35.245Z
api-integration.js:780 Test 18000 - Parsed end time (local): Mon Jul 28 2025 14:04:32 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:04:35 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 18000 - End time timestamp: 1753700672000, Current time timestamp: 1753700675245
api-integration.js:787 Test 18000 - Recent completion (3.2s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 10 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=10, Passed=5, Failed=3, Running=2
api-integration.js:2879 Smart merge for test 18000: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 3 raw tests merged to 2 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: 18000): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:22', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: N/A): {tsn_id: '17999', tc_id: null, ts_id: 333, startTime: '2025-07-28 11:04:04', endTime: '2025-07-28 11:04:05', …}
api-integration.js:1643 Deduplicated 3 raw tests to 2 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 2 tests
api-integration.js:1724 Test 17999 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18000 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18000 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #10, releasing lock
api-integration.js:451 Starting poll request #11
api-integration.js:461 Using since_id=17999 for incremental polling (highestRecentTsnId=18000)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 18000 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 18000 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 18000 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 18000 - Raw end time value: "2025-07-28 11:04:43" (type: string)
api-integration.js:772 Test 18000 - Timezone fix applied: "2025-07-28 11:04:43" -> corrected for 3h offset -> 2025-07-28T11:04:43.000Z
api-integration.js:779 Test 18000 - Parsed end time: 2025-07-28T11:04:43.000Z, Current time: 2025-07-28T11:04:45.238Z
api-integration.js:780 Test 18000 - Parsed end time (local): Mon Jul 28 2025 14:04:43 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:04:45 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 18000 - End time timestamp: 1753700683000, Current time timestamp: 1753700685238
api-integration.js:787 Test 18000 - Recent completion (2.2s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 10 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=10, Passed=5, Failed=3, Running=2
api-integration.js:2879 Smart merge for test 18000: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 3 raw tests merged to 2 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: 18000): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:22', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: N/A): {tsn_id: '17999', tc_id: null, ts_id: 333, startTime: '2025-07-28 11:04:04', endTime: '2025-07-28 11:04:05', …}
api-integration.js:1643 Deduplicated 3 raw tests to 2 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 2 tests
api-integration.js:1724 Test 17999 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18000 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18000 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #11, releasing lock
api-integration.js:451 Starting poll request #12
api-integration.js:461 Using since_id=17999 for incremental polling (highestRecentTsnId=18000)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:2439 Run button clicked for suite: DEMO PE2.1 Sanity Test (312)
api-integration.js:2182 Loading: Running test suite 312...
api-service.js:109 Running test suite with ID: 312
unified-api-service.js:364 Making POST request to: http://localhost:3000/api/suite-runner
unified-api-service.js:403 Request parameters: {ts_id: '312'}
api-integration.js:101 TSN ID is an object, attempting to extract ID: {success: true, testId: undefined, message: 'Test suite 312 started successfully with session ID 18001'}
api-integration.js:109 Extracted session ID from message: 18001
api-integration.js:2215 Success: Test suite 312 started successfully. Run ID: 18001
api-integration.js:2879 Smart merge for test 18000: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 4 raw tests merged to 3 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: 18000): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:22', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: 18001): {status: 'running', type: 'Test Suite', id: '312', ts_id: '312', startTime: Mon Jul 28 2025 14:04:54 GMT+0300 (Eastern European Summer Time), …}
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: N/A): {tsn_id: '17999', tc_id: null, ts_id: 333, startTime: '2025-07-28 11:04:04', endTime: '2025-07-28 11:04:05', …}
api-integration.js:1643 Deduplicated 4 raw tests to 3 processed tests
api-integration.js:1655 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 3 tests
api-integration.js:1724 Test 17999 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18000 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18000 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18001 - Using test data from: activeTests map, status: running
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'DEMO PE2.1 Sanity Test', testId: '312', sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:2200 Loading complete
api-integration.js:628 Test 18001 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 18001 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 18001 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 18001 - Raw end time value: "2025-07-28 11:04:54" (type: string)
api-integration.js:772 Test 18001 - Timezone fix applied: "2025-07-28 11:04:54" -> corrected for 3h offset -> 2025-07-28T11:04:54.000Z
api-integration.js:779 Test 18001 - Parsed end time: 2025-07-28T11:04:54.000Z, Current time: 2025-07-28T11:04:55.233Z
api-integration.js:780 Test 18001 - Parsed end time (local): Mon Jul 28 2025 14:04:54 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:04:55 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 18001 - End time timestamp: 1753700694000, Current time timestamp: 1753700695233
api-integration.js:787 Test 18001 - Recent completion (1.2s ago), keeping as running to allow test results to be written
api-integration.js:628 Test 18000 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 18000 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 18000 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 18000 has completed with status: failed (will be visible for 15-second grace period)
dashboard.js:5 UI NOTIFICATION to be displayed [error]: Suite Finished: PE2.1 Sanity Test - Status: Failed. Cases Passed: 3, Cases Failed: 2.
api-integration.js:1598 Grace period (25s) expired for test 18000, removing from active tests, status: failed
api-integration.js:2879 Smart merge for test 18001: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 4 raw tests merged to 3 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: 18001): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:54', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: N/A): {tsn_id: '18000', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:04:22', endTime: '2025-07-28 11:04:22', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: N/A): {tsn_id: '17999', tc_id: null, ts_id: 333, startTime: '2025-07-28 11:04:04', endTime: '2025-07-28 11:04:05', …}
api-integration.js:1643 Deduplicated 4 raw tests to 3 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 3 tests
api-integration.js:1724 Test 17999 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 18000 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18000 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18001 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:1458 Found 1 new completed tests to count in dashboard
api-integration.js:1472 Passed tests: 0, Failed tests: 1
api-integration.js:1483 Updated dashboard counters: {total: 11, successful: 5, failed: 4}
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 11 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=11, Passed=5, Failed=3, Running=3
api-integration.js:1598 Grace period (25s) expired for test 18000, removing from active tests, status: failed
api-integration.js:2879 Smart merge for test 18001: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 4 raw tests merged to 3 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: 18001): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:54', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 18000 (original map key: N/A): {tsn_id: '18000', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:04:22', endTime: '2025-07-28 11:04:22', …}
api-integration.js:3025 Normalized test data for actual TSN_ID 17999 (original map key: N/A): {tsn_id: '17999', tc_id: null, ts_id: 333, startTime: '2025-07-28 11:04:04', endTime: '2025-07-28 11:04:05', …}
api-integration.js:1643 Deduplicated 4 raw tests to 3 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 3 tests
api-integration.js:1724 Test 17999 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 17999 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 17999 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 17999: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '17999', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 17999: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '17999', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18000 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 18000 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18000 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18000: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18000', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18000: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18000', status: 'running', statusText: 'Running', …}
api-integration.js:1724 Test 18001 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:510 Performing full recent runs refresh
api-integration.js:514 Starting full refresh as part of request #12
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:580 Skipping poll because previous request is still in progress
api-integration.js:533 Full refresh received 100 runs, updating recentRunsCache
api-integration.js:628 Test 18001 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 18001 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 18001 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 18001 - Raw end time value: "2025-07-28 11:05:00" (type: string)
api-integration.js:772 Test 18001 - Timezone fix applied: "2025-07-28 11:05:00" -> corrected for 3h offset -> 2025-07-28T11:05:00.000Z
api-integration.js:779 Test 18001 - Parsed end time: 2025-07-28T11:05:00.000Z, Current time: 2025-07-28T11:05:06.521Z
api-integration.js:780 Test 18001 - Parsed end time (local): Mon Jul 28 2025 14:05:00 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:05:06 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 18001 - End time timestamp: 1753700700000, Current time timestamp: 1753700706521
api-integration.js:787 Test 18001 - Recent completion (6.5s ago), keeping as running to allow test results to be written
api-integration.js:677 Skipping test 18000 as it was previously marked as completed
api-integration.js:677 Skipping test 17999 as it was previously marked as completed
api-integration.js:677 Skipping test 17998 as it was previously marked as completed
api-integration.js:677 Skipping test 17997 as it was previously marked as completed
api-integration.js:677 Skipping test 17996 as it was previously marked as completed
api-integration.js:677 Skipping test 17995 as it was previously marked as completed
api-integration.js:677 Skipping test 17994 as it was previously marked as completed
api-integration.js:677 Skipping test 17993 as it was previously marked as completed
api-integration.js:677 Skipping test 17992 as it was previously marked as completed
api-integration.js:677 Skipping test 17991 as it was previously marked as completed
api-integration.js:677 Skipping test 17990 as it was previously marked as completed
api-integration.js:677 Skipping test 17989 as it was previously marked as completed
api-integration.js:677 Skipping test 17988 as it was previously marked as completed
api-integration.js:677 Skipping test 17987 as it was previously marked as completed
api-integration.js:677 Skipping test 17986 as it was previously marked as completed
api-integration.js:677 Skipping test 17985 as it was previously marked as completed
api-integration.js:677 Skipping test 17984 as it was previously marked as completed
api-integration.js:677 Skipping test 17983 as it was previously marked as completed
api-integration.js:677 Skipping test 17982 as it was previously marked as completed
api-integration.js:677 Skipping test 17981 as it was previously marked as completed
api-integration.js:677 Skipping test 17980 as it was previously marked as completed
api-integration.js:677 Skipping test 17979 as it was previously marked as completed
api-integration.js:677 Skipping test 17978 as it was previously marked as completed
api-integration.js:677 Skipping test 17977 as it was previously marked as completed
api-integration.js:677 Skipping test 17976 as it was previously marked as completed
api-integration.js:677 Skipping test 17975 as it was previously marked as completed
api-integration.js:677 Skipping test 17974 as it was previously marked as completed
api-integration.js:677 Skipping test 17973 as it was previously marked as completed
api-integration.js:677 Skipping test 17972 as it was previously marked as completed
api-integration.js:677 Skipping test 17971 as it was previously marked as completed
api-integration.js:677 Skipping test 17970 as it was previously marked as completed
api-integration.js:677 Skipping test 17969 as it was previously marked as completed
api-integration.js:677 Skipping test 17968 as it was previously marked as completed
api-integration.js:677 Skipping test 17967 as it was previously marked as completed
api-integration.js:677 Skipping test 17966 as it was previously marked as completed
api-integration.js:677 Skipping test 17965 as it was previously marked as completed
api-integration.js:677 Skipping test 17964 as it was previously marked as completed
api-integration.js:677 Skipping test 17963 as it was previously marked as completed
api-integration.js:677 Skipping test 17962 as it was previously marked as completed
api-integration.js:677 Skipping test 17961 as it was previously marked as completed
api-integration.js:677 Skipping test 17960 as it was previously marked as completed
api-integration.js:677 Skipping test 17959 as it was previously marked as completed
api-integration.js:677 Skipping test 17958 as it was previously marked as completed
api-integration.js:677 Skipping test 17957 as it was previously marked as completed
api-integration.js:677 Skipping test 17956 as it was previously marked as completed
api-integration.js:677 Skipping test 17955 as it was previously marked as completed
api-integration.js:677 Skipping test 17954 as it was previously marked as completed
api-integration.js:677 Skipping test 17953 as it was previously marked as completed
api-integration.js:677 Skipping test 17952 as it was previously marked as completed
api-integration.js:677 Skipping test 17951 as it was previously marked as completed
api-integration.js:677 Skipping test 17950 as it was previously marked as completed
api-integration.js:677 Skipping test 17949 as it was previously marked as completed
api-integration.js:677 Skipping test 17948 as it was previously marked as completed
api-integration.js:677 Skipping test 17947 as it was previously marked as completed
api-integration.js:677 Skipping test 17946 as it was previously marked as completed
api-integration.js:677 Skipping test 17945 as it was previously marked as completed
api-integration.js:677 Skipping test 17944 as it was previously marked as completed
api-integration.js:677 Skipping test 17943 as it was previously marked as completed
api-integration.js:677 Skipping test 17942 as it was previously marked as completed
api-integration.js:677 Skipping test 17941 as it was previously marked as completed
api-integration.js:677 Skipping test 17940 as it was previously marked as completed
api-integration.js:677 Skipping test 17939 as it was previously marked as completed
api-integration.js:677 Skipping test 17938 as it was previously marked as completed
api-integration.js:677 Skipping test 17937 as it was previously marked as completed
api-integration.js:677 Skipping test 17936 as it was previously marked as completed
api-integration.js:677 Skipping test 17935 as it was previously marked as completed
api-integration.js:677 Skipping test 17934 as it was previously marked as completed
api-integration.js:677 Skipping test 17933 as it was previously marked as completed
api-integration.js:677 Skipping test 17932 as it was previously marked as completed
api-integration.js:677 Skipping test 17931 as it was previously marked as completed
api-integration.js:677 Skipping test 17930 as it was previously marked as completed
api-integration.js:677 Skipping test 17929 as it was previously marked as completed
api-integration.js:677 Skipping test 17928 as it was previously marked as completed
api-integration.js:677 Skipping test 17927 as it was previously marked as completed
api-integration.js:677 Skipping test 17926 as it was previously marked as completed
api-integration.js:677 Skipping test 17925 as it was previously marked as completed
api-integration.js:677 Skipping test 17924 as it was previously marked as completed
api-integration.js:677 Skipping test 17923 as it was previously marked as completed
api-integration.js:677 Skipping test 17922 as it was previously marked as completed
api-integration.js:677 Skipping test 17921 as it was previously marked as completed
api-integration.js:677 Skipping test 17920 as it was previously marked as completed
api-integration.js:677 Skipping test 17919 as it was previously marked as completed
api-integration.js:677 Skipping test 17918 as it was previously marked as completed
api-integration.js:677 Skipping test 17917 as it was previously marked as completed
api-integration.js:677 Skipping test 17916 as it was previously marked as completed
api-integration.js:677 Skipping test 17915 as it was previously marked as completed
api-integration.js:677 Skipping test 17914 as it was previously marked as completed
api-integration.js:677 Skipping test 17913 as it was previously marked as completed
api-integration.js:677 Skipping test 17912 as it was previously marked as completed
api-integration.js:677 Skipping test 17911 as it was previously marked as completed
api-integration.js:677 Skipping test 17910 as it was previously marked as completed
api-integration.js:677 Skipping test 17909 as it was previously marked as completed
api-integration.js:677 Skipping test 17908 as it was previously marked as completed
api-integration.js:677 Skipping test 17907 as it was previously marked as completed
api-integration.js:677 Skipping test 17906 as it was previously marked as completed
api-integration.js:677 Skipping test 17905 as it was previously marked as completed
api-integration.js:677 Skipping test 17904 as it was previously marked as completed
api-integration.js:677 Skipping test 17903 as it was previously marked as completed
api-integration.js:677 Skipping test 17902 as it was previously marked as completed
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 11 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=11, Passed=6, Failed=4, Running=1
api-integration.js:2879 Smart merge for test 18001: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: 18001): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:54', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 18001 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #12, releasing lock
api-integration.js:451 Starting poll request #13
api-integration.js:461 Using since_id=18000 for incremental polling (highestRecentTsnId=18001)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 18001 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 18001 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 18001 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 18001 - Raw end time value: "2025-07-28 11:05:10" (type: string)
api-integration.js:772 Test 18001 - Timezone fix applied: "2025-07-28 11:05:10" -> corrected for 3h offset -> 2025-07-28T11:05:10.000Z
api-integration.js:779 Test 18001 - Parsed end time: 2025-07-28T11:05:10.000Z, Current time: 2025-07-28T11:05:15.225Z
api-integration.js:780 Test 18001 - Parsed end time (local): Mon Jul 28 2025 14:05:10 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:05:15 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 18001 - End time timestamp: 1753700710000, Current time timestamp: 1753700715225
api-integration.js:787 Test 18001 - Recent completion (5.2s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 11 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=11, Passed=6, Failed=4, Running=1
api-integration.js:2879 Smart merge for test 18001: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: 18001): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:54', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 18001 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #13, releasing lock
api-integration.js:451 Starting poll request #14
api-integration.js:461 Using since_id=18000 for incremental polling (highestRecentTsnId=18001)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 18001 has end_time, keeping active for grace period (status: Running)
api-integration.js:709 Test 18001 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:753 Test 18001 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:757 Test 18001 - Raw end time value: "2025-07-28 11:05:21" (type: string)
api-integration.js:772 Test 18001 - Timezone fix applied: "2025-07-28 11:05:21" -> corrected for 3h offset -> 2025-07-28T11:05:21.000Z
api-integration.js:779 Test 18001 - Parsed end time: 2025-07-28T11:05:21.000Z, Current time: 2025-07-28T11:05:25.575Z
api-integration.js:780 Test 18001 - Parsed end time (local): Mon Jul 28 2025 14:05:21 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Jul 28 2025 14:05:25 GMT+0300 (Eastern European Summer Time)
api-integration.js:781 Test 18001 - End time timestamp: 1753700721000, Current time timestamp: 1753700725575
api-integration.js:787 Test 18001 - Recent completion (4.6s ago), keeping as running to allow test results to be written
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 11 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=11, Passed=6, Failed=4, Running=1
api-integration.js:2879 Smart merge for test 18001: Preserved metadata from activeTests, updated status/timing from cache
api-integration.js:2888 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: 18001): {status: 'running', type: 'Test Suite', id: '312', ts_id: 332, startTime: '2025-07-28 11:04:54', …}
api-integration.js:1643 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 18001 - Using test data from: activeTests map, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #14, releasing lock
api-integration.js:451 Starting poll request #15
api-integration.js:461 Using since_id=18000 for incremental polling (highestRecentTsnId=18001)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:628 Test 18001 has end_time, keeping active for grace period (status: Failed)
api-integration.js:709 Test 18001 completion data: {passed_cases: 3, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:723 Test 18001 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:802 Test 18001 has completed with status: failed (will be visible for 15-second grace period)
dashboard.js:5 UI NOTIFICATION to be displayed [error]: Suite Finished: PE2.1 Sanity Test - Status: Failed. Cases Passed: 3, Cases Failed: 2.
api-integration.js:1598 Grace period (25s) expired for test 18001, removing from active tests, status: failed
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: N/A): {tsn_id: '18001', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:04:54', endTime: '2025-07-28 11:05:00', …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 18001 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:1458 Found 1 new completed tests to count in dashboard
api-integration.js:1472 Passed tests: 0, Failed tests: 1
api-integration.js:1483 Updated dashboard counters: {total: 12, successful: 6, failed: 5}
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 11 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=11, Passed=6, Failed=4, Running=1
api-integration.js:1598 Grace period (25s) expired for test 18001, removing from active tests, status: failed
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: N/A): {tsn_id: '18001', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:04:54', endTime: '2025-07-28 11:05:00', …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 18001 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #15, releasing lock
api-integration.js:451 Starting poll request #16
api-integration.js:461 Using since_id=18000 for incremental polling (highestRecentTsnId=18001)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:677 Skipping test 18001 as it was previously marked as completed
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 11 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=11, Passed=6, Failed=4, Running=1
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: N/A): {tsn_id: '18001', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:04:54', endTime: '2025-07-28 11:05:00', …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 18001 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #16, releasing lock
api-integration.js:451 Starting poll request #17
api-integration.js:461 Using since_id=18000 for incremental polling (highestRecentTsnId=18001)
unified-api-service.js:316 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:321 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:677 Skipping test 18001 as it was previously marked as completed
api-integration.js:1499 Updating dashboard counters from recent runs data
api-integration.js:1510 Found 11 runs for current user: <EMAIL>
api-integration.js:1530 Dashboard counters updated: Total=11, Passed=6, Failed=4, Running=1
api-integration.js:2888 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3025 Normalized test data for actual TSN_ID 18001 (original map key: N/A): {tsn_id: '18001', tc_id: null, ts_id: 332, startTime: '2025-07-28 11:04:54', endTime: '2025-07-28 11:05:00', …}
api-integration.js:1643 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1655 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2280 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1659 Current filter: all Current user: <EMAIL>
api-integration.js:1545 Applying 'all' filter, returning 1 tests
api-integration.js:1724 Test 18001 - Using test data from: recentRunsCache, status: running
api-integration.js:1766 Test 18001 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1773 Test 18001 - Respecting timing-based running status (recent completion)
api-integration.js:1841 Test status for 18001: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1867 Test info for rendering: {testName: 'PE2.1 Sanity Test', testId: 332, tsn_id: '18001', isTestSuite: true, status: 'running', …}
api-integration.js:1922 Rendering card for session 18001: {testName: 'PE2.1 Sanity Test', testId: 332, sessionId: '18001', status: 'running', statusText: 'Running', …}
api-integration.js:555 Completed poll request #17, releasing lock
api-integration.js:451 Starting poll request #18