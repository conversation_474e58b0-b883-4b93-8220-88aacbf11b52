<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 17998<p/>
        <a href="ReportSummary?tsn_id=17998">View Test Result Summary</a><p/> 
        <a href="ReportDetails?tsn_id=17998">View Test Result Details</a><p/> 
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
[2025-07-28T11:02:59.554Z] GET /local/recent-runs?since_id=17996 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17996
Fetching recent runs with filters: { since_id: 17996 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17996
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17996,100]
✅ Database Query: Retrieved 2 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:02:58, end_ts: 2025-07-28 11:02:58
Row 2 start_ts: 2025-07-28 10:53:18, end_ts: 2025-07-28 10:53:50
⏳ Processing 2 sessions in 1 batches...
✅ Processed 2 sessions successfully
Retrieved 2 recent runs
[2025-07-28T11:03:09.562Z] GET /local/recent-runs?since_id=17997 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17997
Fetching recent runs with filters: { since_id: 17997 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17997
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17997,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:02:58, end_ts: 2025-07-28 11:02:58
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:03:19.557Z] GET /local/recent-runs?since_id=17997 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17997
Fetching recent runs with filters: { since_id: 17997 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17997
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17997,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:02:58, end_ts: 2025-07-28 11:03:15
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:03:29.563Z] GET /local/recent-runs?since_id=17997 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17997
Fetching recent runs with filters: { since_id: 17997 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17997
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17997,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:02:58, end_ts: 2025-07-28 11:03:31
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:03:39.554Z] GET /local/recent-runs?since_id=17997 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17997
Fetching recent runs with filters: { since_id: 17997 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17997
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17997,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:02:58, end_ts: 2025-07-28 11:03:42
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:03:45.234Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
Fetching recent runs with filters: {}
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:02:58, end_ts: 2025-07-28 11:03:42
Row 2 start_ts: 2025-07-28 10:53:18, end_ts: 2025-07-28 10:53:50
Row 3 start_ts: 2025-07-28 10:39:46, end_ts: 2025-07-28 10:40:11
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-28T11:03:59.546Z] GET /local/recent-runs?since_id=17997 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17997
Fetching recent runs with filters: { since_id: 17997 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17997
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17997,100]
[2025-07-28T11:04:04.191Z] POST /api/suite-runner from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write       
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (974cffc8...)
   Session ID: 80c152ec69173d47bd02f3615a0b7a2d234fec3eb1812f86e9d6687564db957e
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write       
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (974cffc8...)
   Session ID: 80c152ec69173d47bd02f3615a0b7a2d234fec3eb1812f86e9d6687564db957e
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
[API /suite-runner] Received request with params: { ts_id: '323' }
[API /case-runner] Forwarding request to external API: http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
[Service case-runner] Raw response text received from external API:
--------------------- START RESPONSE ---------------------


<html>
<head>
<title>View Your Test Result</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 17999<p/>
        <a href="ReportSummary?tsn_id=17999">View Test Result Summary</a><p/> 
        <a href="ReportDetails?tsn_id=17999">View Test Result Details</a><p/> 
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
✅ Database Query: Retrieved 2 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:04, end_ts: 2025-07-28 11:04:05
Row 2 start_ts: 2025-07-28 11:02:58, end_ts: 2025-07-28 11:03:42
⏳ Processing 2 sessions in 1 batches...
✅ Processed 2 sessions successfully
Retrieved 2 recent runs
[2025-07-28T11:04:09.547Z] GET /local/recent-runs?since_id=17998 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17998
Fetching recent runs with filters: { since_id: 17998 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17998
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17998,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:04, end_ts: 2025-07-28 11:04:05
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:04:19.561Z] GET /local/recent-runs?since_id=17998 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17998
Fetching recent runs with filters: { since_id: 17998 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17998
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17998,100]
[2025-07-28T11:04:21.316Z] POST /api/suite-runner from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write       
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (974cffc8...)
   Session ID: 80c152ec69173d47bd02f3615a0b7a2d234fec3eb1812f86e9d6687564db957e
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write       
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (974cffc8...)
   Session ID: 80c152ec69173d47bd02f3615a0b7a2d234fec3eb1812f86e9d6687564db957e
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
[API /suite-runner] Received request with params: { ts_id: '312' }
[API /case-runner] Forwarding request to external API: http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
[Service case-runner] Raw response text received from external API:
--------------------- START RESPONSE ---------------------


<html>
<head>
<title>View Your Test Result</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 18000<p/>
        <a href="ReportSummary?tsn_id=18000">View Test Result Summary</a><p/> 
        <a href="ReportDetails?tsn_id=18000">View Test Result Details</a><p/> 
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
✅ Database Query: Retrieved 2 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:22, end_ts: 2025-07-28 11:04:22
Row 2 start_ts: 2025-07-28 11:04:04, end_ts: 2025-07-28 11:04:15
⏳ Processing 2 sessions in 1 batches...
✅ Processed 2 sessions successfully
Retrieved 2 recent runs
[2025-07-28T11:04:29.553Z] GET /local/recent-runs?since_id=17999 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17999
Fetching recent runs with filters: { since_id: 17999 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17999
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17999,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:22, end_ts: 2025-07-28 11:04:32
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:04:39.564Z] GET /local/recent-runs?since_id=17999 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17999
Fetching recent runs with filters: { since_id: 17999 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17999
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17999,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:22, end_ts: 2025-07-28 11:04:43
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:04:49.549Z] GET /local/recent-runs?since_id=17999 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 17999
Fetching recent runs with filters: { since_id: 17999 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 17999
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [17999,100]
[2025-07-28T11:04:54.047Z] POST /api/suite-runner from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write       
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (974cffc8...)
   Session ID: 80c152ec69173d47bd02f3615a0b7a2d234fec3eb1812f86e9d6687564db957e
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write       
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (974cffc8...)
   Session ID: 80c152ec69173d47bd02f3615a0b7a2d234fec3eb1812f86e9d6687564db957e
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
[API /suite-runner] Received request with params: { ts_id: '312' }
[API /case-runner] Forwarding request to external API: http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
[Service case-runner] Raw response text received from external API:
--------------------- START RESPONSE ---------------------


<html>
<head>
<title>View Your Test Result</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 18001<p/>
        <a href="ReportSummary?tsn_id=18001">View Test Result Summary</a><p/> 
        <a href="ReportDetails?tsn_id=18001">View Test Result Details</a><p/> 
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
✅ Database Query: Retrieved 2 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:54, end_ts: 2025-07-28 11:04:54
Row 2 start_ts: 2025-07-28 11:04:22, end_ts: 2025-07-28 11:04:54
⏳ Processing 2 sessions in 1 batches...
✅ Processed 2 sessions successfully
Retrieved 2 recent runs
[2025-07-28T11:04:55.242Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
Fetching recent runs with filters: {}
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:54, end_ts: 2025-07-28 11:05:00
Row 2 start_ts: 2025-07-28 11:04:22, end_ts: 2025-07-28 11:04:54
Row 3 start_ts: 2025-07-28 11:04:04, end_ts: 2025-07-28 11:04:15
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-28T11:05:09.551Z] GET /local/recent-runs?since_id=18000 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18000
Fetching recent runs with filters: { since_id: 18000 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18000
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18000,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:54, end_ts: 2025-07-28 11:05:10
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:05:19.559Z] GET /local/recent-runs?since_id=18000 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18000
Fetching recent runs with filters: { since_id: 18000 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18000
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18000,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-28 11:04:54, end_ts: 2025-07-28 11:05:21
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-07-28T11:05:29.555Z] GET /local/recent-runs?since_id=18000 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read        
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18000
Fetching recent runs with filters: { since_id: 18000 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18000
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?    
[GET_RECENT_RUNS] Final Params: [18000,100]
✅ Database Query: Retrieved 1 test sessions